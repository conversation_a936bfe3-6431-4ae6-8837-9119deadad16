import 'dart:io';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:file_picker/file_picker.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PdfService {
  /// التحقق من الأذونات المطلوبة حسب إصدار Android
  Future<bool> _requestStoragePermissions() async {
    if (!Platform.isAndroid) {
      return true; // iOS لا يحتاج أذونات خاصة
    }

    try {
      // التحقق من إصدار Android
      final isAndroid13Plus = await _isAndroid13OrHigher();
      
      if (isAndroid13Plus) {
        // Android 13+ يستخدم Storage Access Framework
        // لا نحتاج أذونات خاصة - file_picker سيتعامل مع كل شيء
        print('Android 13+ detected - using Storage Access Framework');
        return true;
      } else {
        // Android 12 وما دون - نحتاج READ_EXTERNAL_STORAGE فقط
        print('Android 12 or below - requesting storage permission');
        var status = await Permission.storage.status;
        
        if (!status.isGranted) {
          status = await Permission.storage.request();
        }
        
        if (status.isPermanentlyDenied) {
          print('Storage permission permanently denied');
          await openAppSettings();
          return false;
        }
        
        return status.isGranted;
      }
    } catch (e) {
      print('Error checking permissions: $e');
      // في حالة الخطأ، نجرب المتابعة
      return true;
    }
  }

  /// التحقق من إصدار Android
  Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;
    
    try {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.version.sdkInt >= 33; // Android 13 = API 33
    } catch (e) {
      print('Error getting Android version: $e');
      return false;
    }
  }

  /// اختيار ملف PDF من الجهاز
  Future<Map<String, dynamic>?> pickPdfFile() async {
    try {
      print('Starting PDF file selection...');

      // 1. التحقق من الأذونات
      final hasPermission = await _requestStoragePermissions();
      if (!hasPermission) {
        throw Exception(
          'إذن الوصول إلى التخزين مطلوب لاختيار الملفات. '
          'يرجى منح الإذن من إعدادات التطبيق.'
        );
      }

      print('Permissions granted, opening file picker...');

      // 2. فتح منتقي الملفات
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
        dialogTitle: 'اختر ملف PDF',
        // إعدادات إضافية لـ Android
        withData: false, // تحسين الأداء
        withReadStream: false,
      );

      if (result == null || result.files.isEmpty) {
        print('User cancelled file selection');
        return null;
      }

      final platformFile = result.files.first;
      final filePath = platformFile.path;

      if (filePath == null) {
        print('Error: File path is null');
        throw Exception(
          'لم يتم الحصول على مسار الملف. يرجى المحاولة مرة أخرى.'
        );
      }

      print('Selected file path: $filePath');

      // 3. التحقق من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        print('Error: File does not exist at path: $filePath');
        throw Exception(
          'لم يتم العثور على الملف المحدد. يرجى المحاولة مرة أخرى.'
        );
      }

      // 4. التحقق من حجم الملف
      final fileSize = await file.length();
      final fileSizeInMB = fileSize / (1024 * 1024);
      print('File size: ${fileSize} bytes (${fileSizeInMB.toStringAsFixed(2)} MB)');

      // حد أقصى 100 ميجابايت مع تحذير للملفات الكبيرة
      if (fileSize > 100 * 1024 * 1024) {
        throw Exception(
          'حجم الملف كبير جداً (${fileSizeInMB.toStringAsFixed(1)} ميجابايت). '
          'يرجى اختيار ملف أصغر من 100 ميجابايت.'
        );
      }

      // تحذير للملفات الكبيرة (أكبر من 20 ميجابايت)
      bool isLargeFile = fileSizeInMB > 20;
      if (isLargeFile) {
        print('Warning: Large file detected (${fileSizeInMB.toStringAsFixed(2)} MB)');
      }

      print('File validation successful');
      return {
        'file': file,
        'fileName': platformFile.name,
        'fileSize': fileSize,
      };

    } catch (e) {
      print('Error in pickPdfFile: $e');
      
      // إعادة رمي الأخطاء المعروفة
      if (e is Exception) {
        rethrow;
      }
      
      // خطأ عام
      throw Exception(
        'فشل في اختيار ملف PDF. يرجى التأكد من اختيار ملف صالح.'
      );
    }
  }
  
  /// استخراج النص من ملف PDF مع معالجة محسنة للملفات الكبيرة
  Future<String> extractTextFromPdf(File file) async {
    PdfDocument? document;
    try {
      print('Starting text extraction from PDF...');

      // التحقق من وجود الملف
      if (!await file.exists()) {
        print('Error: PDF file does not exist at path: ${file.path}');
        throw Exception('ملف PDF المحدد غير موجود.');
      }

      // التحقق من حجم الملف
      final fileSize = await file.length();
      final fileSizeInMB = fileSize / (1024 * 1024);
      print('Processing PDF file: ${fileSizeInMB.toStringAsFixed(2)} MB');

      // قراءة بيانات الملف بطريقة محسنة للملفات الكبيرة
      print('Reading PDF file bytes...');
      Uint8List bytes;

      try {
        bytes = await file.readAsBytes();
      } catch (e) {
        throw Exception('فشل في قراءة الملف. قد يكون الملف كبير جداً أو تالف.');
      }

      if (bytes.isEmpty) {
        throw Exception('الملف فارغ أو تالف.');
      }

      // إنشاء مستند PDF مع معالجة الأخطاء
      print('Creating PDF document...');
      try {
        document = PdfDocument(inputBytes: bytes);
      } catch (e) {
        throw Exception('فشل في تحليل ملف PDF. قد يكون الملف تالف أو محمي بكلمة مرور.');
      }

      if (document.pages.count == 0) {
        throw Exception('ملف PDF لا يحتوي على صفحات.');
      }

      print('PDF has ${document.pages.count} pages');

      // استخراج النص مع معالجة محسنة للملفات الكبيرة
      final extractor = PdfTextExtractor(document);
      final buffer = StringBuffer();

      // تحديد عدد الصفحات المراد معالجتها (حد أقصى للملفات الكبيرة)
      int maxPagesToProcess = document.pages.count;
      if (fileSizeInMB > 50) {
        // للملفات الكبيرة جداً، معالجة أول 100 صفحة فقط
        maxPagesToProcess = math.min(100, document.pages.count);
        print('Large file detected. Processing first $maxPagesToProcess pages only.');
      } else if (fileSizeInMB > 20) {
        // للملفات الكبيرة، معالجة أول 200 صفحة
        maxPagesToProcess = math.min(200, document.pages.count);
        print('Medium-large file detected. Processing first $maxPagesToProcess pages.');
      }

      // استخراج النص من الصفحات مع معالجة الأخطاء
      int successfulPages = 0;
      int failedPages = 0;

      for (int i = 0; i < maxPagesToProcess; i++) {
        try {
          final pageText = extractor.extractText(
            startPageIndex: i,
            endPageIndex: i
          );

          if (pageText.trim().isNotEmpty) {
            buffer.write(pageText.trim());
            buffer.write('\n\n');
            successfulPages++;
          }

          // تحديث التقدم كل 10 صفحات للملفات الكبيرة
          if ((i + 1) % 10 == 0 && fileSizeInMB > 10) {
            print('Processed ${i + 1}/$maxPagesToProcess pages...');
          }

          // فحص طول النص لتجنب استهلاك الذاكرة المفرط
          if (buffer.length > 50000) {
            print('Text buffer getting large, stopping at page ${i + 1}');
            break;
          }

        } catch (e) {
          print('Error extracting text from page ${i + 1}: $e');
          failedPages++;
          // متابعة مع الصفحات الأخرى
        }
      }

      print('Text extraction completed: $successfulPages successful, $failedPages failed pages');

      String extractedText = buffer.toString().trim();

      if (extractedText.isEmpty) {
        throw Exception(
          'لم يتم العثور على نص في ملف PDF. '
          'قد يكون الملف يحتوي على صور فقط أو محمي بكلمة مرور.'
        );
      }

      // تحديد طول النص لتجنب قيود واجهة برمجة التطبيقات
      final originalLength = extractedText.length;
      if (extractedText.length > 25000) {
        print('Text too long ($originalLength chars), truncating to 25000 characters');
        extractedText = extractedText.substring(0, 25000) + '\n\n[تم اقتطاع النص للحصول على أفضل النتائج...]';
      }

      print('Final text length: ${extractedText.length} characters');
      return extractedText;

    } catch (e) {
      print('Error in extractTextFromPdf: $e');

      if (e is Exception) {
        rethrow;
      }

      throw Exception('فشل في استخراج النص من ملف PDF: ${e.toString()}');
    } finally {
      // تأكد من تحرير الموارد
      document?.dispose();
    }
  }
}
