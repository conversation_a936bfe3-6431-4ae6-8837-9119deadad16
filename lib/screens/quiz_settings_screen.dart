import 'package:flutter/material.dart';
import 'dart:io';
import 'dart:math'; // إضافة لاستخدام دالة min

import '../models/quiz_model.dart';
import '../services/pdf_service.dart';
import '../services/quiz_service.dart';
import '../services/premium_service.dart';
import 'quiz_taking_screen.dart';
import 'subscription_screen.dart';

class QuizSettingsScreen extends StatefulWidget {
  @override
  _QuizSettingsScreenState createState() => _QuizSettingsScreenState();
}

class _QuizSettingsScreenState extends State<QuizSettingsScreen> {
  final PdfService _pdfService = PdfService();
  final QuizService _quizService = QuizService();
  final PremiumService _premiumService = PremiumService();

  int _questionCount = 10;
  QuizDifficulty _difficulty = QuizDifficulty.medium;
  bool _isLoading = false;
  bool _isPremiumUser = false;
  bool _isLargeFile = false;
  DateTime? _lastWarningTime; // لتتبع آخر مرة تم عرض التحذير

  @override
  void initState() {
    super.initState();
    _checkPremiumStatus();
  }

  Future<void> _checkPremiumStatus() async {
    final isPremium = await _premiumService.isPremiumUser();
    setState(() {
      _isPremiumUser = isPremium;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "إعدادات الاختبار",
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SafeArea(
          child: _isLoading
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        color: Color(0xFF30BEA2),
                        strokeWidth: 4,
                      ),
                      SizedBox(height: 30),
                      Text(
                        _isLargeFile
                          ? 'جاري معالجة الملف الكبير...'
                          : 'جاري معالجة الملف وإنشاء الاختبار...',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[700],
                        ),
                      ),
                      SizedBox(height: 15),
                      if (_isLargeFile)
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                          margin: EdgeInsets.symmetric(horizontal: 40),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade50,
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(color: Colors.orange.shade200),
                          ),
                          child: Column(
                            children: [
                              Icon(
                                Icons.schedule,
                                color: Colors.orange.shade600,
                                size: 24,
                              ),
                              SizedBox(height: 8),
                              Text(
                                'الملف كبير، قد تستغرق المعالجة وقتاً أطول من المعتاد',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.orange.shade700,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        )
                      else
                        Text(
                          'يرجى الانتظار...',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                    ],
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // بطاقة إعدادات الاختبار
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              spreadRadius: 2,
                              blurRadius: 5,
                              offset: Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            // عدد الأسئلة
                            Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.format_list_numbered,
                                        color: Color(0xFF30BEA2),
                                      ),
                                      SizedBox(width: 10),
                                      Text(
                                        "عدد الأسئلة",
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 10),
                                  Text(
                                    "$_questionCount سؤال",
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  SizedBox(height: 10),
                                  Slider(
                                    value: _questionCount.toDouble(),
                                    min: 1,
                                    max: 40, // دائماً 40 للجميع
                                    divisions: 39,
                                    activeColor: Color(0xFF30BEA2),
                                    inactiveColor: Color(0xFF30BEA2).withValues(alpha: 0.2),
                                    label: _questionCount.toString(),
                                    onChanged: (value) {
                                      final newValue = value.toInt();

                                      // إذا كان المستخدم غير مشترك ويحاول تجاوز 10 أسئلة
                                      if (!_isPremiumUser && newValue > 10) {
                                        // التحقق من عدم عرض التحذير مؤخراً (خلال آخر ثانيتين)
                                        final now = DateTime.now();
                                        if (_lastWarningTime == null ||
                                            now.difference(_lastWarningTime!).inSeconds >= 2) {
                                          _lastWarningTime = now;

                                          // عرض رسالة تحذيرية
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            SnackBar(
                                              content: Row(
                                                children: [
                                                  Icon(Icons.lock, color: Colors.white, size: 20),
                                                  SizedBox(width: 8),
                                                  Expanded(
                                                    child: Text(
                                                      'يجب الاشتراك لزيادة عدد الأسئلة عن 10',
                                                      style: TextStyle(fontWeight: FontWeight.bold),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              backgroundColor: Colors.orange.shade600,
                                              behavior: SnackBarBehavior.floating,
                                              duration: Duration(seconds: 2),
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(10),
                                              ),
                                              action: SnackBarAction(
                                                label: 'اشترك الآن',
                                                textColor: Colors.white,
                                                onPressed: _showSubscriptionScreen,
                                              ),
                                            ),
                                          );
                                        }
                                        return; // لا تحديث القيمة
                                      }

                                      setState(() {
                                        _questionCount = newValue;
                                      });
                                    },
                                  ),
                                  if (!_isPremiumUser)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 12.0),
                                      child: Container(
                                        padding: EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: Colors.orange.shade50,
                                          borderRadius: BorderRadius.circular(10),
                                          border: Border.all(color: Colors.orange.shade200),
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(
                                              Icons.info_outline,
                                              color: Colors.orange.shade600,
                                              size: 20,
                                            ),
                                            SizedBox(width: 8),
                                            Expanded(
                                              child: Text(
                                                'أنت محدود بـ 10 أسئلة فقط. اشترك للحصول على 40 سؤال',
                                                style: TextStyle(
                                                  color: Colors.orange.shade700,
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 13,
                                                ),
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: _showSubscriptionScreen,
                                              child: Container(
                                                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                decoration: BoxDecoration(
                                                  color: Colors.orange.shade600,
                                                  borderRadius: BorderRadius.circular(6),
                                                ),
                                                child: Text(
                                                  'اشترك',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            Divider(),
                            // مستوى الصعوبة
                            Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.battery_alert,
                                        color: Color(0xFF30BEA2),
                                      ),
                                      SizedBox(width: 10),
                                      Text(
                                        "مستوى الصعوبة",
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 20),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                                    children: [
                                      _buildDifficultyOption(
                                        label: "سهل",
                                        value: QuizDifficulty.easy,
                                        icon: Icons.sentiment_satisfied_outlined,
                                      ),
                                      _buildDifficultyOption(
                                        label: "متوسط",
                                        value: QuizDifficulty.medium,
                                        icon: Icons.sentiment_neutral_outlined,
                                      ),
                                      _buildDifficultyOption(
                                        label: "صعب",
                                        value: QuizDifficulty.hard,
                                        icon: Icons.sentiment_very_dissatisfied_outlined,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 30),
                      // زر اختيار الملف
                      ElevatedButton.icon(
                        onPressed: _selectPdfAndCreateQuiz,
                        icon: Icon(Icons.folder_open, color: Colors.white),
                        label: Text(
                          "اختيار ملف PDF من الجهاز",
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFF30BEA2),
                          padding: EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          elevation: 4,
                          shadowColor: Color(0xFF30BEA2).withOpacity(0.4),
                        ),
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildDifficultyOption({
    required String label,
    required QuizDifficulty value,
    required IconData icon,
  }) {
    final isSelected = _difficulty == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _difficulty = value;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? Color(0xFF30BEA2).withOpacity(0.2) : Colors.white,
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: isSelected ? Color(0xFF30BEA2) : Colors.grey.shade300,
            width: 1.5,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Color(0xFF30BEA2) : Colors.grey,
              size: 28,
            ),
            SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Color(0xFF30BEA2) : Colors.grey,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectPdfAndCreateQuiz() async {
    try {
      // التحقق من إمكانية إنشاء اختبار جديد
      final canCreate = await _premiumService.canCreateQuiz();

      if (!canCreate) {
        // إذا لم يكن بإمكان المستخدم إنشاء اختبار، اعرض صفحة الاشتراك
        _showSubscriptionScreen();
        return;
      }

      // اختيار ملف PDF من ذاكرة الجهاز الداخلية
      final result = await _pdfService.pickPdfFile();

      if (result != null) {
        final file = result['file'] as File;
        final fileName = result['fileName'] as String;

        // التحقق من أن الملف موجود بالفعل
        if (!await file.exists()) {
          throw Exception('لم يتم العثور على الملف المحدد بعد اختياره. يرجى المحاولة مرة أخرى.');
        }

        // التحقق من حجم الملف
        final fileSize = await file.length();
        final fileSizeInMB = fileSize / (1024 * 1024);

        setState(() {
          _isLoading = true;
          _isLargeFile = fileSizeInMB > 5; // اعتبار الملف كبير إذا كان أكبر من 5 ميجابايت
        });

        // عرض رسالة مناسبة حسب حجم الملف
        if (mounted) {
          String message;
          Color backgroundColor;

          if (_isLargeFile) {
            message = 'الملف الذي قمت بتحميله كبير (${fileSizeInMB.toStringAsFixed(1)} ميجابايت). يرجى الانتظار لبعض الوقت حتى يكتمل عمل الاختبارات...';
            backgroundColor = Colors.orange;
          } else {
            message = 'جاري معالجة الملف وإنشاء الاختبار...';
            backgroundColor = Colors.blue;
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor,
              behavior: SnackBarBehavior.floating,
              duration: Duration(seconds: _isLargeFile ? 5 : 3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }

        // استخراج النص من ملف PDF
        final pdfText = await _pdfService.extractTextFromPdf(file);

        // إنشاء أسئلة الاختبار باستخدام الذكاء الاصطناعي
        final questions = await _quizService.generateQuizQuestions(
          pdfText,
          _questionCount,
          _difficulty,
        );

        // إنشاء اختبار جديد
        final newQuiz = Quiz(
          title: fileName,
          questions: questions,
          createdAt: DateTime.now(),
          difficulty: _difficulty,
        );

        // تحميل الاختبارات الحالية
        final quizzes = await _quizService.loadQuizzes();

        // إضافة الاختبار الجديد وحفظه
        quizzes.add(newQuiz);
        await _quizService.saveQuizzes(quizzes);

        // تسجيل استخدام التجربة المجانية إذا لم يكن مستخدم مدفوع
        final isPremium = await _premiumService.isPremiumUser();
        if (!isPremium) {
          await _premiumService.markFreeTrialAsUsed();
        }

        if (mounted) { // التحقق قبل تحديث الحالة أو التنقل
          setState(() {
            _isLoading = false;
            _isLargeFile = false; // إعادة تعيين حالة الملف الكبير
          });

          // عرض رسالة نجاح
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم إنشاء الاختبار بنجاح! يحتوي على ${questions.length} سؤال'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              duration: Duration(seconds: 2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );

          // الانتقال إلى شاشة إجراء الاختبار
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => QuizTakingScreen(quiz: newQuiz),
            ),
          );
        }
      }
      // إذا كان result هو null (المستخدم ألغى الاختيار)، لا تفعل شيئًا أو أظهر رسالة بسيطة
    } catch (e, s) { // إضافة s لتتبع الخطأ stack trace
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isLargeFile = false; // إعادة تعيين حالة الملف الكبير في حالة الخطأ
        });
      }

      print('حدث خطأ في _selectPdfAndCreateQuiz: $e'); // طباعة الخطأ للمساعدة في التصحيح
      print('Stack trace: $s'); // طباعة تتبع الخطأ

      String errorMessage = 'حدث خطأ غير متوقع أثناء معالجة الملف.'; // رسالة افتراضية محسنة
      String errorDetails = e.toString();

      if (errorDetails.contains('إذن الوصول إلى وحدة التخزين مطلوب') || errorDetails.contains('Permission.storage')) {
        errorMessage = 'يرجى منح التطبيق إذن الوصول إلى وحدة التخزين من إعدادات الهاتف للمتابعة.';
      } else if (errorDetails.contains('لم يتم العثور على الملف') ||
                 errorDetails.contains('File does not exist') ||
                 errorDetails.contains('ملف PDF المحدد غير موجود')) {
        errorMessage = 'لم يتم العثور على الملف الذي اخترته. يرجى التأكد من أن الملف موجود وحاول مرة أخرى.';
      } else if (errorDetails.contains('فشل في اختيار ملف PDF')) {
        errorMessage = 'حدث خطأ أثناء محاولة اختيار الملف. يرجى المحاولة مرة أخرى والتأكد من اختيار ملف PDF صالح من ذاكرة الجهاز.';
      } else if (errorDetails.contains('فشل في استخراج النص من ملف PDF')) {
        errorMessage = 'حدث خطأ أثناء محاولة قراءة محتوى ملف PDF. قد يكون الملف تالفًا أو غير مدعوم.';
      } else if (errorDetails.contains('generateQuizQuestions')) {
        errorMessage = 'حدث خطأ أثناء إنشاء أسئلة الاختبار من محتوى الملف. قد يكون محتوى الملف غير مناسب.';
      } else {
        // لرسائل الخطأ الأخرى غير المعروفة، اعرض جزءًا من الخطأ الفعلي
        errorMessage = 'حدث خطأ أثناء معالجة الملف. التفاصيل: ${errorDetails.substring(0, min(errorDetails.length, 120))}'; // زيادة طول التفاصيل المعروضة
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.redAccent, // تغيير طفيف في اللون للتمييز
            behavior: SnackBarBehavior.floating,
            duration: Duration(seconds: 6), // زيادة المدة للسماح بقراءة الرسائل الأطول
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _showSubscriptionScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubscriptionScreen(),
      ),
    );
  }
}
